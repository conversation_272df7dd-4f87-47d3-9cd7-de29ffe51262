<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <title>CodTech AI Chatbot</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://cdn.jsdelivr.net/npm/marked/marked.min.js"></script>
  <style>
    :root {
  --color-dark: #27445D;       /* Dark navy */
  --color-mid: #497D74;        /* Dark green */
  --color-light-mid: #71BBB2;  /* Soft teal */
  --color-light: #EFE9D5;      /* Cream */
  --text-light: #ffffff;
  --text-dark: #000000;
}


    [data-theme="dark"] {
      --bg-light: #1e1f22;
      --bg-mid: #2b2c2f;
      --bg-dark: #121314;
      --text-color: #ffffff;
      --user-msg-bg: #0d6efd;
      --bot-msg-bg: #444654;
    }

    [data-theme="light"] {
      --bg-light: #f1efec;
      --bg-mid: #d4c9be;
      --bg-dark: #ffffff;
      --text-color: #000000;
      --user-msg-bg: #123458;
      --bot-msg-bg: #d4c9be;
    }

    body {
      font-family: var(--font);
      background-color: var(--bg-light);
      margin: 0;
      padding: 0;
      color: var(--text-color);
      display: flex;
      justify-content: center;
      align-items: center;
      height: 100vh;
    }

    .chat-container {
      display: flex;
      flex-direction: column;
      width: 100%;
      max-width: 900px;
      height: 100vh;
      background-color: var(--bg-dark);
      box-shadow: 0 0 10px rgba(0,0,0,0.5);
    }

    .chat-header {
      padding: 20px;
      background-color: var(--bg-mid);
      color: var(--text-color);
      font-size: 22px;
      font-weight: bold;
      display: flex;
      justify-content: space-between;
      align-items: center;
    }

    .chat-box {
      flex-grow: 1;
      overflow-y: auto;
      padding: 20px;
      background-color: var(--bg-light);
      display: flex;
      flex-direction: column;
    }

    .message-row {
      display: flex;
      flex-direction: row;
      margin-bottom: 14px;
    }

    .message {
      max-width: 80%;
      padding: 12px 18px;
      border-radius: 16px;
      line-height: 1.6;
      white-space: pre-wrap;
      word-wrap: break-word;
      font-size:medium;
    }

    .user-message {
      background-color: var(--user-msg-bg);
      color: #fff;
      align-self: flex-end;
      margin-left: auto;
      border-bottom-right-radius: 0;
    }

    .bot-message {
      background-color: var(--bot-msg-bg);
      color: #fffdfd;
      align-self: flex-start;
      margin-right: auto;
      border-bottom-left-radius: 0;
    }

    .chat-form {
      padding: 16px;
      background-color: var(--bg-mid);
      display: flex;
      gap: 10px;
    }

    textarea {
      flex-grow: 1;
      padding: 12px;
      font-size: 16px;
      border-radius: 8px;
      border: none;
      resize: none;
      height: 50px;
      background-color: #fff;
      color: #000;
    }

    input[type="submit"], .clear-btn, .toggle-theme-btn {
      padding: 12px 18px;
      background-color: var(--user-msg-bg);
      color: #fff;
      border: none;
      border-radius: 8px;
      cursor: pointer;
    }

    input[type="submit"]:hover, .clear-btn:hover, .toggle-theme-btn:hover {
      background-color: #0f2f4e;
    }

    .loader {
      display: none;
      margin: 10px auto;
      text-align: center;
      color: #999;
      font-size: 14px;
      font-style: italic;
    }
    body {
  background-color: var(--color-dark);
  color: var(--text-light); /* This makes font white for dark background */
}

  </style>
</head>
<body data-theme="dark">
  <div class="chat-container">
    <div class="chat-header">
      <span>💬 CodTech AI Chat</span>
      <div>
        <button class="toggle-theme-btn" onclick="toggleTheme()">🌓 Dark/Light</button>
        <form method="POST" action="/clear" style="display:inline-block; margin-left:10px;">
          <button class="clear-btn" type="submit">🗑️ Clear Chat</button>
        </form>
      </div>
    </div>

    <div class="chat-box" id="chat-box">
      {% for chat in session.get('history', []) %}
        <div class="message-row">
          <div class="message user-message">{{ chat.user }}</div>
        </div>
        <div class="message-row">
          <div class="message bot-message"><span class="markdown">{{ chat.bot }}</span></div>
        </div>
      {% endfor %}
    </div>

    <div class="loader" id="loader">🤖 Thinking...</div>

    <form class="chat-form" method="POST" id="chat-form">
      <textarea name="query" id="query" placeholder="Ask something..." required autofocus></textarea>
      <input type="submit" value="Send">
    </form>
  </div>

  <script>
    const form = document.getElementById('chat-form');
    const queryBox = document.getElementById('query');
    const chatBox = document.getElementById('chat-box');
    const loader = document.getElementById('loader');

    queryBox.addEventListener("keydown", function (e) {
      if (e.key === "Enter" && !e.shiftKey) {
        e.preventDefault();
        form.requestSubmit();
      }
    });

    form.addEventListener("submit", function () {
      loader.style.display = "block";
      setTimeout(() => loader.style.display = "none", 1000);
      queryBox.value = "";
    });

    window.onload = () => {
      chatBox.scrollTop = chatBox.scrollHeight;
      document.querySelectorAll(".markdown").forEach(el => {
        el.innerHTML = marked.parse(el.innerText);
      });
    };

    function toggleTheme() {
      const body = document.body;
      const currentTheme = body.getAttribute('data-theme');
      body.setAttribute('data-theme', currentTheme === 'dark' ? 'light' : 'dark');
    }
  </script>
</body>
</html>
