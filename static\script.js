// script.js

document.addEventListener("DOMContentLoaded", () => {
  const form = document.querySelector("form");
  const responseBox = document.querySelector(".response-box");
  const inputBox = document.querySelector("textarea");

  // Add typing animation effect to bot response
  if (responseBox) {
    const fullText = responseBox.innerText;
    responseBox.innerText = "";
    let i = 0;
    const type = () => {
      if (i < fullText.length) {
        responseBox.innerText += fullText.charAt(i);
        i++;
        setTimeout(type, 20);
      }
    };
    type();
  }

  // Auto-resize the textarea
  inputBox.addEventListener("input", () => {
    inputBox.style.height = "auto";
    inputBox.style.height = `${inputBox.scrollHeight}px`;
  });

  // Replace certain keywords with emojis
  form.addEventListener("submit", () => {
    let value = inputBox.value;
    value = value.replace(/hello|hi/gi, "👋");
    value = value.replace(/thank you|thanks/gi, "🙏");
    value = value.replace(/weather/gi, "🌦️");
    inputBox.value = value;
  });
});
